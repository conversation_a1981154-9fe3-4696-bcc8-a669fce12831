#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
印章识别运行脚本

简单易用的命令行工具，用于识别PDF和图片中的印章。
"""

import os
import sys
from pathlib import Path

from seal_recognizer import SealRecognizer, quick_recognize_seals
from config import config


def print_banner():
    """打印程序横幅"""
    print("=" * 60)
    print("    印章识别工具")
    print("    基于PaddleOCR SealRecognition")
    print("    支持PDF和图片文件")
    print("=" * 60)
    print()


def list_files_in_directory(directory: str = "."):
    """列出目录中的支持文件"""
    dir_path = Path(directory)
    
    # 支持的文件扩展名
    supported_extensions = {'.pdf', '.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.webp'}
    files = []
    
    for ext in supported_extensions:
        files.extend(dir_path.glob(f"*{ext}"))
        files.extend(dir_path.glob(f"*{ext.upper()}"))
    
    if not files:
        print(f"在目录 {dir_path} 中未找到支持的文件")
        return []
    
    print(f"找到 {len(files)} 个支持的文件:")
    for i, file_path in enumerate(files, 1):
        size_mb = file_path.stat().st_size / (1024 * 1024)
        file_type = "PDF" if file_path.suffix.lower() == '.pdf' else "图片"
        print(f"  {i}. {file_path.name} ({size_mb:.1f} MB) [{file_type}]")
    
    return files


def interactive_mode():
    """交互式模式"""
    print_banner()
    
    while True:
        print("\n请选择操作:")
        print("1. 识别单个文件")
        print("2. 批量识别当前目录中的文件")
        print("3. 批量识别指定目录中的文件")
        print("4. 查看配置信息")
        print("5. 退出")
        
        choice = input("\n请输入选项 (1-5): ").strip()
        
        if choice == "1":
            single_file_mode()
        elif choice == "2":
            batch_mode_current_dir()
        elif choice == "3":
            batch_mode_custom_dir()
        elif choice == "4":
            show_config()
        elif choice == "5":
            print("再见！")
            break
        else:
            print("无效选项，请重新选择。")


def single_file_mode():
    """单文件识别模式"""
    print("\n--- 单文件识别模式 ---")
    
    # 列出当前目录的支持文件
    files = list_files_in_directory()
    
    if not files:
        file_path = input("\n请输入文件路径: ").strip()
        if not file_path:
            print("未输入文件路径")
            return
    else:
        print("\n选择文件:")
        print("0. 输入自定义路径")
        
        choice = input("请输入文件编号: ").strip()
        
        if choice == "0":
            file_path = input("请输入文件路径: ").strip()
            if not file_path:
                print("未输入文件路径")
                return
        else:
            try:
                index = int(choice) - 1
                if 0 <= index < len(files):
                    file_path = str(files[index])
                else:
                    print("无效的文件编号")
                    return
            except ValueError:
                print("请输入有效的数字")
                return
    
    # 选择设备
    device = input("\n选择运行设备 (cpu/gpu) [默认:cpu]: ").strip() or "cpu"
    
    # 开始识别
    print(f"\n开始识别: {file_path}")
    print(f"使用设备: {device}")
    
    try:
        results = quick_recognize_seals(file_path, device=device)
        
        # 显示结果
        summary = results["summary"]
        print(f"\n识别完成！")
        print(f"总页数: {results['total_pages']}")
        print(f"处理时间: {results['processing_time']:.2f} 秒")
        print(f"检测到印章: {summary['total_seals_detected']} 个")
        print(f"识别到文本: {summary['total_texts_recognized']} 个")
        print(f"成功页面: {summary['successful_pages']} 页")
        
        if summary['total_seals_detected'] > 0:
            print("\n印章详情:")
            for page in results["pages"]:
                if "seals" in page and page["seals"]:
                    for seal in page["seals"]:
                        detection_count = seal.get("detection", {}).get("total_boxes", 0)
                        recognition_text = seal.get("recognition", {}).get("combined_text", "")
                        
                        if detection_count > 0:
                            print(f"  第{page['page_number']}页 印章{seal['seal_id']}: {detection_count}个检测框")
                            if recognition_text:
                                print(f"    文本: {recognition_text}")
        
    except Exception as e:
        print(f"识别失败: {e}")


def batch_mode_current_dir():
    """批量识别当前目录"""
    print("\n--- 批量识别模式 (当前目录) ---")
    
    files = list_files_in_directory()
    if not files:
        return
    
    confirm = input(f"\n确认识别这 {len(files)} 个文件？(y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("已取消")
        return
    
    # 选择设备
    device = input("选择运行设备 (cpu/gpu) [默认:cpu]: ").strip() or "cpu"
    
    try:
        recognizer = SealRecognizer(device=device)
        results = recognizer.batch_recognize(".")
        
        print(f"\n批量识别完成！")
        print(f"处理文件: {len(results['processed_files'])} 个")
        print(f"失败文件: {len(results['failed_files'])} 个")
        
        # 显示每个文件的结果
        for filename, result in results['results'].items():
            seal_count = result['summary']['total_seals_detected']
            text_count = result['summary']['total_texts_recognized']
            print(f"  {filename}: {seal_count}个印章, {text_count}个文本")
        
        if results['failed_files']:
            print("\n失败文件:")
            for failed in results['failed_files']:
                print(f"  {failed['file']}: {failed['error']}")
        
    except Exception as e:
        print(f"批量识别失败: {e}")


def batch_mode_custom_dir():
    """批量识别指定目录"""
    print("\n--- 批量识别模式 (指定目录) ---")
    
    directory = input("请输入目录路径: ").strip()
    if not directory:
        print("未输入目录路径")
        return
    
    if not os.path.isdir(directory):
        print(f"目录不存在: {directory}")
        return
    
    # 列出目录中的支持文件
    files = list_files_in_directory(directory)
    if not files:
        return
    
    confirm = input(f"\n确认识别这 {len(files)} 个文件？(y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("已取消")
        return
    
    # 选择设备
    device = input("选择运行设备 (cpu/gpu) [默认:cpu]: ").strip() or "cpu"
    
    try:
        recognizer = SealRecognizer(device=device)
        results = recognizer.batch_recognize(directory)
        
        print(f"\n批量识别完成！")
        print(f"处理文件: {len(results['processed_files'])} 个")
        print(f"失败文件: {len(results['failed_files'])} 个")
        
        # 显示每个文件的结果
        for filename, result in results['results'].items():
            seal_count = result['summary']['total_seals_detected']
            text_count = result['summary']['total_texts_recognized']
            print(f"  {filename}: {seal_count}个印章, {text_count}个文本")
        
        if results['failed_files']:
            print("\n失败文件:")
            for failed in results['failed_files']:
                print(f"  {failed['file']}: {failed['error']}")
        
    except Exception as e:
        print(f"批量识别失败: {e}")


def show_config():
    """显示配置信息"""
    print("\n--- 当前配置 ---")
    config.print_config()


def main():
    """主函数"""
    # 检查命令行参数
    if len(sys.argv) > 1:
        # 命令行模式
        input_path = sys.argv[1]
        
        # 设备参数
        device = "cpu"
        if len(sys.argv) > 2:
            device = sys.argv[2]
        
        # 检查是否为批量模式
        if os.path.isdir(input_path):
            print(f"批量识别目录: {input_path}")
            print(f"使用设备: {device}")
            try:
                recognizer = SealRecognizer(device=device)
                results = recognizer.batch_recognize(input_path)
                print(f"批量识别完成: {len(results['processed_files'])} 个文件")
                
                # 显示简要结果
                for filename, result in results['results'].items():
                    seal_count = result['summary']['total_seals_detected']
                    text_count = result['summary']['total_texts_recognized']
                    print(f"  {filename}: {seal_count}个印章, {text_count}个文本")
                    
            except Exception as e:
                print(f"识别失败: {e}")
                sys.exit(1)
        else:
            # 单文件模式
            print(f"识别文件: {input_path}")
            print(f"使用设备: {device}")
            
            try:
                results = quick_recognize_seals(input_path, device=device)
                summary = results["summary"]
                print(f"识别完成:")
                print(f"  检测到印章: {summary['total_seals_detected']} 个")
                print(f"  识别到文本: {summary['total_texts_recognized']} 个")
                print(f"  处理时间: {results['processing_time']:.2f} 秒")
                
            except Exception as e:
                print(f"识别失败: {e}")
                sys.exit(1)
    else:
        # 交互式模式
        interactive_mode()


if __name__ == "__main__":
    main() 