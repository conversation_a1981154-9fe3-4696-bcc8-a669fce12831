import os
from typing import Dict, Any
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class SealRecognitionConfig:
    """印章识别配置类"""
    
    def __init__(self):
        """初始化配置"""
        # 设备配置
        self.device = os.getenv("DEVICE", "cpu")  # cpu, gpu:0, npu:0, etc.
        
        # 功能开关
        self.use_doc_orientation_classify = os.getenv("USE_DOC_ORIENTATION", "false").lower() == "true"
        self.use_doc_unwarping = os.getenv("USE_DOC_UNWARPING", "false").lower() == "true"
        self.use_layout_detection = os.getenv("USE_LAYOUT_DETECTION", "true").lower() == "true"
        
        # 版面检测参数
        self.layout_threshold = float(os.getenv("LAYOUT_THRESHOLD", "0.5"))
        self.layout_nms = os.getenv("LAYOUT_NMS", "true").lower() == "true"
        self.layout_unclip_ratio = float(os.getenv("LAYOUT_UNCLIP_RATIO", "1.0"))
        
        # 印章检测参数
        self.seal_det_thresh = float(os.getenv("SEAL_DET_THRESH", "0.2"))
        self.seal_det_box_thresh = float(os.getenv("SEAL_DET_BOX_THRESH", "0.6"))
        self.seal_det_unclip_ratio = float(os.getenv("SEAL_DET_UNCLIP_RATIO", "0.5"))
        self.seal_det_limit_side_len = int(os.getenv("SEAL_DET_LIMIT_SIDE_LEN", "736"))
        
        # 文本识别参数
        self.seal_rec_score_thresh = float(os.getenv("SEAL_REC_SCORE_THRESH", "0.0"))
        self.text_recognition_batch_size = int(os.getenv("TEXT_REC_BATCH_SIZE", "1"))
        
        # 性能配置
        self.enable_hpi = os.getenv("ENABLE_HPI", "false").lower() == "true"
        self.use_tensorrt = os.getenv("USE_TENSORRT", "false").lower() == "true"
        self.precision = os.getenv("PRECISION", "fp32")
        self.enable_mkldnn = os.getenv("ENABLE_MKLDNN", "true").lower() == "true"
        self.cpu_threads = int(os.getenv("CPU_THREADS", "8"))
        
        # 输出配置
        self.output_dir = os.getenv("OUTPUT_DIR", "seal_output")
        self.save_images = os.getenv("SAVE_IMAGES", "true").lower() == "true"
        self.save_json = os.getenv("SAVE_JSON", "true").lower() == "true"
    
    def get_seal_recognition_params(self) -> Dict[str, Any]:
        """获取SealRecognition初始化参数"""
        return {
            "use_doc_orientation_classify": self.use_doc_orientation_classify,
            "use_doc_unwarping": self.use_doc_unwarping,
            "use_layout_detection": self.use_layout_detection,
            "layout_threshold": self.layout_threshold,
            "layout_nms": self.layout_nms,
            "layout_unclip_ratio": self.layout_unclip_ratio,
            "seal_det_thresh": self.seal_det_thresh,
            "seal_det_box_thresh": self.seal_det_box_thresh,
            "seal_det_unclip_ratio": self.seal_det_unclip_ratio,
            "seal_det_limit_side_len": self.seal_det_limit_side_len,
            "seal_rec_score_thresh": self.seal_rec_score_thresh,
            "text_recognition_batch_size": self.text_recognition_batch_size,
            "device": self.device,
            "enable_hpi": self.enable_hpi,
            "use_tensorrt": self.use_tensorrt,
            "precision": self.precision,
            "enable_mkldnn": self.enable_mkldnn,
            "cpu_threads": self.cpu_threads,
        }
    
    def print_config(self):
        """打印配置信息"""
        print("印章识别配置:")
        print(f"  设备: {self.device}")
        print(f"  文档方向分类: {self.use_doc_orientation_classify}")
        print(f"  文档图像矫正: {self.use_doc_unwarping}")
        print(f"  版面检测: {self.use_layout_detection}")
        print(f"  印章检测阈值: {self.seal_det_thresh}")
        print(f"  印章框阈值: {self.seal_det_box_thresh}")
        print(f"  文本识别阈值: {self.seal_rec_score_thresh}")
        print(f"  输出目录: {self.output_dir}")


# 全局配置实例
config = SealRecognitionConfig()


def create_env_template():
    """创建.env文件模板"""
    template = """# 设备配置
DEVICE=cpu

# 功能开关
USE_DOC_ORIENTATION=false
USE_DOC_UNWARPING=false
USE_LAYOUT_DETECTION=true

# 版面检测参数
LAYOUT_THRESHOLD=0.5
LAYOUT_NMS=true
LAYOUT_UNCLIP_RATIO=1.0

# 印章检测参数
SEAL_DET_THRESH=0.2
SEAL_DET_BOX_THRESH=0.6
SEAL_DET_UNCLIP_RATIO=0.5
SEAL_DET_LIMIT_SIDE_LEN=736

# 文本识别参数
SEAL_REC_SCORE_THRESH=0.0
TEXT_REC_BATCH_SIZE=1

# 性能配置
ENABLE_HPI=false
USE_TENSORRT=false
PRECISION=fp32
ENABLE_MKLDNN=true
CPU_THREADS=8

# 输出配置
OUTPUT_DIR=seal_output
SAVE_IMAGES=true
SAVE_JSON=true
"""
    
    if not os.path.exists(".env"):
        with open(".env", "w", encoding="utf-8") as f:
            f.write(template)
        print("已创建.env配置文件模板")
    else:
        print(".env文件已存在")


if __name__ == "__main__":
    create_env_template()
    config.print_config() 