#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
印章识别器

基于PaddleOCR的SealRecognition实现的印章识别工具。
支持直接处理PDF文件和图片文件。
"""

import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

import pandas as pd
from loguru import logger

from config import config

try:
    from paddleocr import SealRecognition
except ImportError:
    logger.error("未找到SealRecognition，请安装: pip install paddleocr")
    raise


class SealRecognizer:
    """印章识别器
    
    基于PaddleOCR的SealRecognition引擎，可以直接处理PDF和图片文件。
    
    功能特点:
    - 直接处理PDF文件，无需转换
    - 专业的印章检测和识别
    - 版面检测和文档预处理
    - 详细的结构化输出
    - 批量处理支持
    """
    
    def __init__(self, **kwargs):
        """初始化识别器
        
        Args:
            **kwargs: SealRecognition的额外参数，会覆盖配置文件中的参数
        """
        # 配置
        self.config = config
        
        # 创建输出目录
        self.output_dir = Path(self.config.output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 合并参数
        self.seal_params = self.config.get_seal_recognition_params()
        self.seal_params.update(kwargs)
        
        # 初始化SealRecognition
        self.seal_pipeline = None
        self._init_seal_recognition()
        
        # 配置日志
        self._setup_logging()
        
        logger.info("印章识别器初始化完成")
    
    def _setup_logging(self):
        """设置日志记录"""
        log_file = self.output_dir / f"seal_recognition_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        logger.add(log_file, rotation="10 MB", retention="10 days", encoding="utf-8")
    
    def _init_seal_recognition(self):
        """初始化SealRecognition"""
        try:
            logger.info(f"正在初始化SealRecognition，参数: {self.seal_params}")
            self.seal_pipeline = SealRecognition(**self.seal_params)
            logger.info("SealRecognition初始化成功")
            
        except Exception as e:
            logger.error(f"SealRecognition初始化失败: {e}")
            raise
    
    def recognize_file(
        self,
        input_path: Union[str, Path],
        **predict_kwargs
    ) -> Dict[str, Any]:
        """识别单个文件中的印章
        
        Args:
            input_path: 输入文件路径（支持PDF、图片等）
            **predict_kwargs: predict方法的额外参数
            
        Returns:
            识别结果字典
        """
        input_path = Path(input_path)
        if not input_path.exists():
            raise FileNotFoundError(f"文件不存在: {input_path}")
        
        logger.info(f"开始识别文件: {input_path}")
        
        # 创建任务输出目录
        task_dir = self.output_dir / f"{input_path.stem}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        task_dir.mkdir(exist_ok=True)
        
        start_time = time.time()
        
        # 执行识别
        try:
            results = self.seal_pipeline.predict(str(input_path), **predict_kwargs)
            processing_time = time.time() - start_time
            
            logger.info(f"识别完成，耗时: {processing_time:.2f}秒")
            
            # 处理结果
            processed_results = self._process_results(
                results, 
                input_path, 
                task_dir, 
                processing_time
            )
            
            # 保存结果
            if self.config.save_json or self.config.save_images:
                self._save_results(results, processed_results, task_dir)
            
            return processed_results
            
        except Exception as e:
            logger.error(f"识别失败: {e}")
            raise
    
    def _process_results(
        self, 
        results: List[Any], 
        input_path: Path, 
        task_dir: Path, 
        processing_time: float
    ) -> Dict[str, Any]:
        """处理识别结果"""
        
        processed_results = {
            "input_path": str(input_path),
            "processing_time": processing_time,
            "timestamp": datetime.now().isoformat(),
            "total_pages": len(results),
            "pages": [],
            "summary": {}
        }
        
        total_seals = 0
        total_texts = 0
        
        for page_idx, result in enumerate(results):
            try:
                # 获取结果的JSON数据
                result_json = result.json if hasattr(result, 'json') else {}
                
                page_info = {
                    "page_index": page_idx,
                    "page_number": page_idx + 1,
                    "layout_detection": {},
                    "seals": [],
                    "preprocessing": {}
                }
                
                # 处理版面检测结果
                if "layout_det_res" in result_json:
                    layout_res = result_json["layout_det_res"]
                    if "boxes" in layout_res:
                        page_info["layout_detection"] = {
                            "total_regions": len(layout_res["boxes"]),
                            "regions": layout_res["boxes"]
                        }
                
                # 处理印章识别结果
                if "seal_res_list" in result_json:
                    seal_list = result_json["seal_res_list"]
                    
                    for seal_idx, seal_res in enumerate(seal_list):
                        seal_info = {
                            "seal_id": seal_idx,
                            "detection": {},
                            "recognition": {},
                            "preprocessing": {}
                        }
                        
                        # 文档预处理信息
                        if "doc_preprocessor_res" in seal_res:
                            preprocess_res = seal_res["doc_preprocessor_res"]
                            seal_info["preprocessing"] = {
                                "angle": preprocess_res.get("angle", -1),
                                "model_settings": preprocess_res.get("model_settings", {})
                            }
                        
                        # 检测结果
                        if "dt_polys" in seal_res and "dt_scores" in seal_res:
                            dt_polys = seal_res["dt_polys"]
                            dt_scores = seal_res["dt_scores"]
                            
                            detection_boxes = []
                            for poly, score in zip(dt_polys, dt_scores):
                                if isinstance(poly, list) and len(poly) > 0:
                                    # 计算边界框
                                    if isinstance(poly[0], list):
                                        x_coords = [point[0] for point in poly]
                                        y_coords = [point[1] for point in poly]
                                    else:
                                        # 如果是扁平列表，重新组织
                                        x_coords = poly[::2]
                                        y_coords = poly[1::2]
                                    
                                    bbox = {
                                        "polygon": poly,
                                        "bbox": {
                                            "x_min": min(x_coords),
                                            "y_min": min(y_coords),
                                            "x_max": max(x_coords),
                                            "y_max": max(y_coords),
                                            "width": max(x_coords) - min(x_coords),
                                            "height": max(y_coords) - min(y_coords)
                                        },
                                        "confidence": float(score)
                                    }
                                    detection_boxes.append(bbox)
                            
                            seal_info["detection"] = {
                                "total_boxes": len(detection_boxes),
                                "boxes": detection_boxes
                            }
                            total_seals += len(detection_boxes)
                        
                        # 识别结果
                        if "rec_texts" in seal_res and "rec_scores" in seal_res:
                            rec_texts = seal_res["rec_texts"]
                            rec_scores = seal_res["rec_scores"]
                            rec_polys = seal_res.get("rec_polys", [])
                            
                            recognition_results = []
                            for i, (text, score) in enumerate(zip(rec_texts, rec_scores)):
                                text_info = {
                                    "text": text,
                                    "confidence": float(score),
                                    "polygon": rec_polys[i] if i < len(rec_polys) else []
                                }
                                recognition_results.append(text_info)
                            
                            seal_info["recognition"] = {
                                "total_texts": len(recognition_results),
                                "texts": recognition_results,
                                "combined_text": " ".join(rec_texts) if rec_texts else ""
                            }
                            total_texts += len(recognition_results)
                        
                        page_info["seals"].append(seal_info)
                
                processed_results["pages"].append(page_info)
                
            except Exception as e:
                logger.error(f"处理第{page_idx}页结果时出错: {e}")
                page_info = {
                    "page_index": page_idx,
                    "page_number": page_idx + 1,
                    "error": str(e)
                }
                processed_results["pages"].append(page_info)
        
        # 生成汇总
        processed_results["summary"] = {
            "total_seals_detected": total_seals,
            "total_texts_recognized": total_texts,
            "successful_pages": len([p for p in processed_results["pages"] if "error" not in p]),
            "failed_pages": len([p for p in processed_results["pages"] if "error" in p])
        }
        
        return processed_results
    
    def _save_results(
        self, 
        raw_results: List[Any], 
        processed_results: Dict[str, Any], 
        task_dir: Path
    ):
        """保存识别结果"""
        
        # 保存处理后的JSON结果
        if self.config.save_json:
            json_file = task_dir / "seal_recognition_results.json"
            with open(json_file, "w", encoding="utf-8") as f:
                json.dump(processed_results, f, ensure_ascii=False, indent=2)
            
            # 保存原始结果的JSON（如果需要）
            raw_json_file = task_dir / "raw_results.json"
            raw_data = []
            for result in raw_results:
                if hasattr(result, 'json'):
                    raw_data.append(result.json)
            
            with open(raw_json_file, "w", encoding="utf-8") as f:
                json.dump(raw_data, f, ensure_ascii=False, indent=2)
        
        # 保存可视化图片
        if self.config.save_images:
            for page_idx, result in enumerate(raw_results):
                try:
                    img_dir = task_dir / f"page_{page_idx + 1}"
                    img_dir.mkdir(exist_ok=True)
                    result.save_to_img(str(img_dir))
                except Exception as e:
                    logger.warning(f"保存第{page_idx + 1}页图片失败: {e}")
        
        # 生成汇总报告
        self._generate_summary_report(processed_results, task_dir)
    
    def _generate_summary_report(self, results: Dict[str, Any], task_dir: Path):
        """生成汇总报告"""
        
        # 生成CSV汇总
        if results["summary"]["total_seals_detected"] > 0:
            seal_data = []
            for page in results["pages"]:
                if "seals" in page:
                    for seal in page["seals"]:
                        if "detection" in seal and "boxes" in seal["detection"]:
                            for box_idx, box in enumerate(seal["detection"]["boxes"]):
                                # 获取识别文本
                                text = ""
                                if "recognition" in seal and "texts" in seal["recognition"]:
                                    texts = [t["text"] for t in seal["recognition"]["texts"]]
                                    text = " ".join(texts)
                                
                                seal_data.append({
                                    "页码": page["page_number"],
                                    "印章ID": seal["seal_id"],
                                    "检测框ID": box_idx,
                                    "置信度": f"{box['confidence']:.3f}",
                                    "X坐标": box["bbox"]["x_min"],
                                    "Y坐标": box["bbox"]["y_min"],
                                    "宽度": box["bbox"]["width"],
                                    "高度": box["bbox"]["height"],
                                    "识别文本": text
                                })
            
            if seal_data:
                df = pd.DataFrame(seal_data)
                csv_file = task_dir / "seal_summary.csv"
                df.to_csv(csv_file, index=False, encoding="utf-8-sig")
        
        # 生成文本报告
        report_file = task_dir / "recognition_report.txt"
        with open(report_file, "w", encoding="utf-8") as f:
            f.write("印章识别报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"输入文件: {results['input_path']}\n")
            f.write(f"识别时间: {results['timestamp']}\n")
            f.write(f"总页数: {results['total_pages']}\n")
            f.write(f"处理时间: {results['processing_time']:.2f} 秒\n")
            f.write(f"检测到的印章: {results['summary']['total_seals_detected']} 个\n")
            f.write(f"识别到的文本: {results['summary']['total_texts_recognized']} 个\n")
            f.write(f"成功页面: {results['summary']['successful_pages']} 页\n")
            f.write(f"失败页面: {results['summary']['failed_pages']} 页\n")
            f.write("\n详细结果:\n")
            
            for page in results["pages"]:
                if "error" not in page:
                    f.write(f"\n第{page['page_number']}页:\n")
                    if "seals" in page:
                        for seal in page["seals"]:
                            detection_count = 0
                            recognition_text = ""
                            
                            if "detection" in seal:
                                detection_count = seal["detection"].get("total_boxes", 0)
                            
                            if "recognition" in seal:
                                recognition_text = seal["recognition"].get("combined_text", "")
                            
                            f.write(f"  印章{seal['seal_id']}: {detection_count}个检测框")
                            if recognition_text:
                                f.write(f", 文本: {recognition_text}")
                            f.write("\n")
        
        logger.info(f"结果已保存到: {task_dir}")
    
    def batch_recognize(
        self,
        input_directory: str,
        pattern: str = "*",
        **predict_kwargs
    ) -> Dict[str, Any]:
        """批量识别目录中的文件
        
        Args:
            input_directory: 输入目录
            pattern: 文件匹配模式
            **predict_kwargs: predict方法的额外参数
            
        Returns:
            批量识别结果
        """
        input_dir = Path(input_directory)
        if not input_dir.exists():
            raise FileNotFoundError(f"目录不存在: {input_dir}")
        
        # 查找文件（支持PDF和常见图片格式）
        supported_extensions = {'.pdf', '.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.webp'}
        files = []
        for ext in supported_extensions:
            files.extend(input_dir.glob(f"{pattern}{ext}"))
            files.extend(input_dir.glob(f"{pattern}{ext.upper()}"))
        
        if not files:
            logger.warning(f"在目录 {input_dir} 中未找到支持的文件")
            return {"message": "未找到支持的文件", "files": []}
        
        logger.info(f"找到 {len(files)} 个文件，开始批量识别")
        
        batch_results = {
            "directory": str(input_dir),
            "pattern": pattern,
            "total_files": len(files),
            "processed_files": [],
            "failed_files": [],
            "results": {}
        }
        
        for file_path in files:
            try:
                logger.info(f"开始识别: {file_path.name}")
                result = self.recognize_file(file_path, **predict_kwargs)
                batch_results["results"][file_path.name] = result
                batch_results["processed_files"].append(file_path.name)
                
            except Exception as e:
                logger.error(f"识别 {file_path.name} 时出错: {e}")
                batch_results["failed_files"].append({
                    "file": file_path.name,
                    "error": str(e)
                })
        
        # 保存批量识别汇总
        if self.config.save_json:
            batch_summary_file = self.output_dir / f"batch_recognition_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(batch_summary_file, "w", encoding="utf-8") as f:
                json.dump(batch_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"批量识别完成，成功: {len(batch_results['processed_files'])}, 失败: {len(batch_results['failed_files'])}")
        return batch_results


# 便捷函数
def quick_recognize_seals(input_path: str, **kwargs) -> Dict[str, Any]:
    """快速印章识别
    
    Args:
        input_path: 输入文件路径
        **kwargs: 额外参数
        
    Returns:
        识别结果
    """
    recognizer = SealRecognizer()
    return recognizer.recognize_file(input_path, **kwargs)


if __name__ == "__main__":
    # 测试用法
    import argparse
    
    parser = argparse.ArgumentParser(description="印章识别工具")
    parser.add_argument("input_path", help="输入文件或目录路径")
    parser.add_argument("--batch", "-b", action="store_true", help="批量处理模式")
    parser.add_argument("--device", "-d", default="cpu", help="运行设备")
    
    args = parser.parse_args()
    
    try:
        recognizer = SealRecognizer(device=args.device)
        
        if args.batch:
            # 批量处理模式
            results = recognizer.batch_recognize(args.input_path)
            print(f"批量识别完成: {results['total_files']} 个文件")
        else:
            # 单文件处理模式
            results = recognizer.recognize_file(args.input_path)
            print(f"识别完成，检测到 {results['summary']['total_seals_detected']} 个印章")
            print(f"识别到 {results['summary']['total_texts_recognized']} 个文本")
            
    except Exception as e:
        logger.error(f"识别失败: {e}")
        exit(1) 