# 印章识别工具

基于PaddleOCR SealRecognition的专业印章识别工具，支持直接处理PDF文件和图片。

## 功能特点

- 🔍 **专业印章识别**: 基于PaddleOCR的SealRecognition引擎
- 📄 **直接处理PDF**: 无需转换，直接读取PDF文件
- 🎯 **精确检测定位**: 印章位置检测和边界框标注
- 📝 **文本识别**: 识别印章内的文字内容
- 📊 **详细报告**: 生成JSON、CSV和文本格式的结果报告
- 🔧 **灵活配置**: 支持环境变量配置和参数调节
- 📁 **批量处理**: 支持目录级别的批量文件处理
- 🖼️ **可视化输出**: 生成带标注的图片结果

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行识别

#### 交互式模式（推荐新手）
```bash
python run_seal.py
```

#### 命令行模式
```bash
# 识别单个文件
python run_seal.py "合同.pdf"

# 指定使用GPU
python run_seal.py "合同.pdf" gpu

# 批量识别目录
python run_seal.py "/path/to/pdf/directory"
```

### 3. 查看结果

识别完成后，结果将保存在 `seal_output` 目录中：
- `seal_recognition_results.json` - 完整的识别结果
- `raw_results.json` - 原始API返回结果
- `seal_summary.csv` - 印章汇总表
- `recognition_report.txt` - 识别报告
- `page_X/` - 各页面的可视化图片

## 项目结构

```
├── config.py              # 配置管理
├── seal_recognizer.py     # 核心识别器
├── run_seal.py           # 运行脚本
├── requirements.txt      # 依赖包
├── README.md            # 说明文档
├── 施工合同HX.pdf        # 示例文件
└── 采购合同1-材料购销合同.pdf  # 示例文件
```

## 配置说明

### 环境变量配置

创建 `.env` 文件或设置环境变量：

```bash
# 设备配置
DEVICE=cpu                 # cpu, gpu:0, npu:0 等

# 功能开关
USE_DOC_ORIENTATION=false  # 文档方向分类
USE_DOC_UNWARPING=false   # 文档图像矫正
USE_LAYOUT_DETECTION=true # 版面检测

# 版面检测参数
LAYOUT_THRESHOLD=0.5      # 版面检测置信度阈值
LAYOUT_NMS=true          # 是否使用NMS后处理
LAYOUT_UNCLIP_RATIO=1.0  # 检测框扩张系数

# 印章检测参数
SEAL_DET_THRESH=0.2      # 印章检测像素阈值
SEAL_DET_BOX_THRESH=0.6  # 印章检测框阈值
SEAL_DET_UNCLIP_RATIO=0.5 # 印章检测扩张系数
SEAL_DET_LIMIT_SIDE_LEN=736 # 图像边长限制

# 文本识别参数
SEAL_REC_SCORE_THRESH=0.0 # 文本识别阈值
TEXT_REC_BATCH_SIZE=1     # 文本识别批处理大小

# 性能配置
ENABLE_HPI=false         # 是否启用高性能推理
USE_TENSORRT=false       # 是否启用TensorRT
PRECISION=fp32           # 计算精度
ENABLE_MKLDNN=true       # 是否启用MKL-DNN
CPU_THREADS=8            # CPU线程数

# 输出配置
OUTPUT_DIR=seal_output   # 输出目录
SAVE_IMAGES=true        # 是否保存可视化图片
SAVE_JSON=true          # 是否保存JSON结果
```

### 生成配置模板

```bash
python config.py
```

## 使用示例

### Python脚本调用

```python
from seal_recognizer import quick_recognize_seals

# 快速识别
results = quick_recognize_seals("合同.pdf")
print(f"检测到 {results['summary']['total_seals_detected']} 个印章")
print(f"识别到 {results['summary']['total_texts_recognized']} 个文本")

# 指定设备
results = quick_recognize_seals("合同.pdf", device="gpu")
```

### 高级用法

```python
from seal_recognizer import SealRecognizer

# 创建识别器，自定义参数
recognizer = SealRecognizer(
    device="gpu",
    use_layout_detection=True,
    seal_det_box_thresh=0.7
)

# 识别单个文件
results = recognizer.recognize_file("合同.pdf")

# 批量识别
batch_results = recognizer.batch_recognize("./pdf_directory")

# 使用额外的predict参数
results = recognizer.recognize_file(
    "合同.pdf",
    use_layout_detection=False,  # 临时关闭版面检测
    seal_det_thresh=0.3         # 临时调整检测阈值
)
```

## 识别结果格式

### JSON结果结构
```json
{
  "input_path": "合同.pdf",
  "processing_time": 15.5,
  "timestamp": "2024-01-01T12:00:00",
  "total_pages": 5,
  "summary": {
    "total_seals_detected": 3,
    "total_texts_recognized": 8,
    "successful_pages": 5,
    "failed_pages": 0
  },
  "pages": [
    {
      "page_number": 1,
      "layout_detection": {
        "total_regions": 2,
        "regions": [...]
      },
      "seals": [
        {
          "seal_id": 0,
          "detection": {
            "total_boxes": 1,
            "boxes": [
              {
                "polygon": [[x1,y1], [x2,y2], ...],
                "bbox": {
                  "x_min": 100, "y_min": 200,
                  "width": 80, "height": 80
                },
                "confidence": 0.95
              }
            ]
          },
          "recognition": {
            "total_texts": 2,
            "texts": [
              {"text": "公司印章", "confidence": 0.98},
              {"text": "2024年", "confidence": 0.92}
            ],
            "combined_text": "公司印章 2024年"
          },
          "preprocessing": {
            "angle": 0
          }
        }
      ]
    }
  ]
}
```

### CSV结果示例
| 页码 | 印章ID | 检测框ID | 置信度 | X坐标 | Y坐标 | 宽度 | 高度 | 识别文本 |
|------|--------|----------|--------|-------|-------|------|------|----------|
| 1    | 0      | 0        | 0.950  | 100   | 200   | 80   | 80   | 公司印章 |
| 3    | 0      | 0        | 0.876  | 150   | 300   | 75   | 75   | 审核专用章 |

## 支持的文件格式

- **PDF**: 支持多页PDF文档
- **图片**: PNG, JPG, JPEG, BMP, TIFF, WebP

## 故障排除

### 常见问题

1. **PaddleOCR安装问题**
   ```bash
   # 使用清华源
   pip install paddleocr -i https://pypi.tuna.tsinghua.edu.cn/simple
   
   # 或使用conda
   conda install paddlepaddle -c paddle
   ```

2. **GPU支持问题**
   ```bash
   # 检查CUDA版本
   nvidia-smi
   
   # 安装对应的paddlepaddle-gpu
   pip install paddlepaddle-gpu
   ```

3. **内存不足**
   - 使用CPU模式：设置 `DEVICE=cpu`
   - 降低批处理大小：`TEXT_REC_BATCH_SIZE=1`
   - 关闭不必要功能：`USE_LAYOUT_DETECTION=false`

4. **识别精度问题**
   - 调整检测阈值：`SEAL_DET_BOX_THRESH=0.7`
   - 调整文本识别阈值：`SEAL_REC_SCORE_THRESH=0.5`
   - 启用文档预处理：`USE_DOC_UNWARPING=true`

### 性能优化

- **CPU优化**: 
  - 设置 `ENABLE_MKLDNN=true`
  - 调整 `CPU_THREADS=8`
  
- **GPU加速**: 
  - 设置 `DEVICE=gpu`
  - 启用 `USE_TENSORRT=true`（需要TensorRT）
  
- **内存优化**: 
  - 设置 `SAVE_IMAGES=false` 减少存储
  - 降低图像分辨率限制：`SEAL_DET_LIMIT_SIDE_LEN=512`

## 技术原理

1. **版面检测**: 自动识别文档中的印章区域
2. **印章检测**: 精确定位印章的位置和边界
3. **文本识别**: 识别印章内的文字内容
4. **结果整合**: 结构化输出检测和识别结果

## 系统要求

- Python 3.7+
- 4GB+ RAM（推荐8GB+）
- 足够的磁盘空间存储结果

### GPU要求（可选）
- CUDA 10.2+ 或 11.x
- 2GB+ 显存

## 许可证

此工具基于PaddleOCR构建，遵循Apache 2.0许可证。 